fly-fit/
├── assets/            # Static assets (images, fonts, etc.)
├── docs/              # Documentation files
├── functions/         # Firebase Cloud Functions
├── scripts/           # Build and utility scripts
├── src/               # Source code
│   ├── api/           # API interfaces and services
│   ├── components/    # React components
│   ├── contexts/      # React contexts
│   ├── data-hooks/    # Data fetching hooks
│   ├── hooks/         # Custom React hooks
│   ├── pages/         # Screen components
│   ├── types/         # TypeScript type definitions
│   └── utils/         # Utility functions
└── ...

› Press a │ open Android
› Press i │ open iOS simulator
› Press w │ open web
› Press r │ reload app
› Press m │ toggle menu
› Press ? │ show all commands

npm update
npx expo install --fix

npx npm-check -u

npm i -g eas-cli
npm install expo@^52.0.0
npx expo install --fix
npx expo-doctor